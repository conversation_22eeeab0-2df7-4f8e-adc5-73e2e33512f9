import { NextRequest, NextResponse } from 'next/server'
import { jwtVerify, type JWTPayload, decodeJwt } from 'jose';

interface AuthResult {
  isValid: boolean;
  payload: any;
  token: string;
}

export default async function middleware(request: NextRequest) {
  // console.log('🔥 MIDDLEWARE TRIGGERED:', request.nextUrl.pathname);
  // /api/auth/ ve /api/pub/ altındaki route'lar için token kontrolü yapma
  if (request.nextUrl.pathname.startsWith('/api/auth/') ||
      request.nextUrl.pathname.startsWith('/api/pub/')) {
    return NextResponse.next();
  }

  // Token kontrolü yap
  const authResult = await authenticateRequest(request);

  if (!authResult.isValid) {
    return new NextResponse(
      JSON.stringify({ success: false, message: 'authentication failed' }),
      { status: 401, headers: { 'content-type': 'application/json' } }
    );
  }

  // Token'ı header'a ekle
  const requestHeaders = new Headers(request.headers);
  requestHeaders.set('token', authResult.token);

  return NextResponse.next({
    request: {
      headers: requestHeaders,
    },
  });
}

async function authenticateRequest(request: NextRequest): Promise<AuthResult> {
  const authHeader = request.headers.get('authorization');
  const bearerToken = authHeader ? authHeader.split(' ') : [];
  const token = bearerToken[1];

  if (!token) {
    return {
      isValid: false,
      payload: null,
      token: '',
    };
  }

  const key = process.env.JWT_KEY;
  if (!key) {
    console.error('JWT_KEY environment variable is not set');
    return {
      isValid: false,
      payload: null,
      token: token,
    };
  }

  try {
    const tokenData = await verifyToken(token, key);
    if (tokenData && tokenData.isValid) {
      return {
        isValid: true,
        payload: tokenData.payload,
        token: token,
      };
    } else {
      // Token geçersizse refresh token ile yenilemeyi dene
      const decodedJWT = decodeJwt(token);
      const refreshResponse = await fetch(request.nextUrl.origin + "/api/auth/refreshtoken", {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ payload: decodedJWT })
      });

      const refreshContent = await refreshResponse.json();
      const refreshToken = refreshContent?.token;

      if (refreshToken) {
        const refreshTokenData = await verifyToken(refreshToken, key);
        if (refreshTokenData && refreshTokenData.isValid) {
          return {
            isValid: true,
            payload: refreshTokenData.payload,
            token: refreshToken,
          };
        }
      }
    }
  } catch (e) {
    console.log('Token verification error:', request.nextUrl.pathname, e);
  }

  return {
    isValid: false,
    payload: null,
    token: token,
  };
}

async function verifyToken(token: string, secret: string): Promise<{ isValid: boolean; payload: any }> {
  try {
    const { payload } = await jwtVerify(token, new TextEncoder().encode(secret));
    if (payload && payload.exp && (Date.now() - payload.exp * 1000) < 0) {
      return {
        isValid: true,
        payload: payload,
      };
    }
  } catch (e) {
    // Token geçersiz
  }

  return {
    isValid: false,
    payload: null,
  };
}

export const config = {
  matcher: ['/api/:path*'],
}
